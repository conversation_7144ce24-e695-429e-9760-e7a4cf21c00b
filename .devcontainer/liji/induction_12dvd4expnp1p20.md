# Proof Tree: 12 divides 4^(n+1) + 20 for every natural number n

## Node Structure

### ROOT_001 [ROOT]
- **Theorem Statement**: Prove that 12 divides 4^(n+1) + 20 for every natural number n
- **Goal**: ∀ n : ℕ, 12 ∣ (4^(n+1) + 20)
- **Status**: [TO_EXPLORE]

### STRATEGY_001 [STRATEGY]
- **Parent Node**: ROOT_001
- **Detailed Plan**: Use modular arithmetic approach by checking divisibility separately modulo 4 and modulo 3 (coprime factors of 12), then combine using Chinese Remainder Theorem
- **Strategy**: Modular arithmetic proof using coprime factorization (12 = 4 × 3)
- **Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Prove 4 ∣ (4^(n+1) + 20) for all n : ℕ
- **Strategy**: Show that 4^(n+1) ≡ 0 (mod 4) and 20 ≡ 0 (mod 4), therefore 4^(n+1) + 20 ≡ 0 (mod 4)
- **Status**: [TO_EXPLORE]

### SUBGOAL_002 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Prove 3 ∣ (4^(n+1) + 20) for all n : ℕ
- **Strategy**: Show that 4 ≡ 1 (mod 3), so 4^(n+1) ≡ 1^(n+1) ≡ 1 (mod 3), and 20 ≡ 2 (mod 3), so 4^(n+1) + 20 ≡ 1 + 2 ≡ 0 (mod 3)
- **Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Combine divisibility by 4 and 3 to prove divisibility by 12
- **Strategy**: Use the fact that gcd(4,3) = 1 and if both 4 ∣ x and 3 ∣ x, then 12 ∣ x
- **Status**: [TO_EXPLORE]
